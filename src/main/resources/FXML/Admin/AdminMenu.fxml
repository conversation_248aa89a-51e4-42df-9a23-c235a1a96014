<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Menu?>
<?import javafx.scene.control.MenuBar?>
<?import javafx.scene.control.MenuItem?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.shape.Circle?>

<AnchorPane fx:id="root" prefHeight="70.0" prefWidth="990.0" styleClass="nav-container" stylesheets="@../../Styles/Admin/AdminMenu.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.store.app.petstore.Controllers.Admin.AdminMenuController">

    <ImageView fitHeight="40.0" fitWidth="153.0" pickOnBounds="true" preserveRatio="true" AnchorPane.leftAnchor="15.0" AnchorPane.topAnchor="15.0">
        <Image url="@../../Images/logo1.png" />
    </ImageView>
    <HBox AnchorPane.rightAnchor="20" AnchorPane.topAnchor="20">

        <HBox AnchorPane.topAnchor="20">
            <MenuBar fx:id="menuBar" prefHeight="36.0" prefWidth="426.0">
                <Menu fx:id="menuStatistics" mnemonicParsing="false" text="Thống kê">
                    <MenuItem fx:id="menuItemOverview" mnemonicParsing="false" text="Tổng quát" />
                    <MenuItem fx:id="menuItemRevenue" mnemonicParsing="false" text="Doanh thu" />
                </Menu>
                <Menu fx:id="menuManagement" mnemonicParsing="false" text="Quản lý">
                    <MenuItem fx:id="menuItemPets" mnemonicParsing="false" text="Thú cưng" />
                    <MenuItem fx:id="menuItemProducts" mnemonicParsing="false" text="Sản phẩm" />
                    <MenuItem fx:id="menuItemDiscounts" mnemonicParsing="false" text="Khuyến mãi" />
                    <MenuItem fx:id="menuItemCustomers" mnemonicParsing="false" text="Khách hàng" />
                    <MenuItem fx:id="menuItemStaff" mnemonicParsing="false" text="Nhân viên" />
                    <MenuItem fx:id="menuItemAccounts" mnemonicParsing="false" text="Tài khoản" />
                </Menu>
                <Menu fx:id="menuRevenue" mnemonicParsing="false" text="Doanh thu" />
                <Menu fx:id="menuLogout" mnemonicParsing="false" text="Đăng xuất" />
                <cursor>
                    <Cursor fx:constant="HAND" />
                </cursor>
            </MenuBar>
        </HBox>
        <VBox>
            <padding>
                <Insets bottom="10" left="10" right="10" />
            </padding>
            <Label fx:id="usernameLabel" contentDisplay="RIGHT" styleClass="label-username" text="admin123" textAlignment="RIGHT" />
            <Label fx:id="nameLabel" contentDisplay="RIGHT" styleClass="label-name" text="Quản trị viên" textAlignment="RIGHT" />
        </VBox>
        <Circle fx:id="userImage" fill="DODGERBLUE" radius="20.0" stroke="BLACK" strokeType="INSIDE" styleClass="user-image" AnchorPane.leftAnchor="18.0" AnchorPane.rightAnchor="18.0" />
    </HBox>

</AnchorPane>
