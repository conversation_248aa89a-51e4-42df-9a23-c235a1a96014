<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.String?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.BarChart?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressBar?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>

<AnchorPane styleClass="root" stylesheets="@../../../Styles/Statistics.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.store.app.petstore.Controllers.Admin.Statistic.RevenueController">

    <VBox alignment="CENTER" spacing="20.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <fx:include source="../AdminMenu.fxml" />
        <HBox spacing="20.0" style="-fx-padding: 20px" AnchorPane.bottomAnchor="20" AnchorPane.topAnchor="180" VBox.vgrow="ALWAYS">
            <VBox styleClass="main-statistic-tag" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
                <HBox alignment="CENTER_LEFT" prefHeight="50" spacing="10.0">
                    <VBox spacing="10">
                        <Label styleClass="main-statistic-text" text="Biểu đồ thống kê doanh thu theo nhân viên" />
                    </VBox>
                </HBox>
                <ProgressBar fx:id="chartProgressBar" prefWidth="200" visible="false" VBox.vgrow="NEVER" />
                <BarChart fx:id="summaryChart" animated="false" legendVisible="false" prefHeight="400" VBox.vgrow="ALWAYS">
                    <xAxis>
                        <CategoryAxis />
                    </xAxis>
                    <yAxis>
                        <NumberAxis label="Doanh thu (triệu VND)" />
                    </yAxis>
                </BarChart>
            </VBox>
            <VBox styleClass="main-statistic-tag" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
                <HBox alignment="CENTER_LEFT" prefHeight="20.0" prefWidth="422.0" spacing="10">
                    <padding>
                        <Insets bottom="20" />
                    </padding>
                    <VBox spacing="10">
                        <Label styleClass="main-statistic-text" text="Doanh thu theo nhân viên" />
                        <HBox spacing="20" />

                    </VBox>
                      <ChoiceBox fx:id="monthChoiceBox" prefHeight="30.0" prefWidth="160" HBox.hgrow="ALWAYS">
                          <styleClass>
                              <String fx:value="choice-box-filter" />
                              <String fx:value="yellow" />
                          </styleClass>

                      </ChoiceBox>
                </HBox>
                <ProgressBar fx:id="staffTableProgressBar" prefWidth="200" visible="false" VBox.vgrow="NEVER" />
                <TableView fx:id="staffRevenueTable" prefHeight="400" styleClass="table-view" VBox.vgrow="ALWAYS">
                    <columnResizePolicy>
                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                    </columnResizePolicy>
                    <columns>
                        <TableColumn fx:id="colNameEmpl" prefWidth="150" text="Tên nhân viên">
                            <cellValueFactory>
                                <PropertyValueFactory property="staffName" />
                            </cellValueFactory>
                        </TableColumn>
                  <TableColumn fx:id="colTotalBIll" prefWidth="150" text="Số hóa đơn">
                     <cellValueFactory>
                        <PropertyValueFactory property="totalOrders" />
                     </cellValueFactory>
                  </TableColumn>
                        <TableColumn fx:id="colTotalMoney" prefWidth="100" text="Doanh thu">
                            <cellValueFactory>
                                <PropertyValueFactory property="totalRevenue" />
                            </cellValueFactory>
                        </TableColumn>
                    </columns>
                </TableView>
            </VBox>
        </HBox>
    </VBox>
</AnchorPane>
